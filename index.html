<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
    <title>FlexoDiv Portfolio</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'brand-dark': '#0A0F1F',
                        'brand-surface': '#1A2035',
                        'brand-surface-light': 'rgba(26, 32, 53, 0.5)',
                        'brand-blue': '#4285F4', // Gemini Blue
                        'brand-purple': '#9B72F5', // Gemini Purple
                        'brand-light': '#E0E8FF',
                        'brand-muted': '#8D98B2',
                    },
                    animation: {
                        'fade-in': 'fadeIn 1s ease-in-out forwards',
                        'slide-in-up': 'slideInUp 0.8s ease-out forwards',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideInUp: {
                            '0%': { transform: 'translateY(30px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        }
                    }
                }
            }
        }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://unpkg.com/@studio-freight/lenis@1.0.42/dist/lenis.min.js"></script>
<script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.3",
    "@google/genai": "https://esm.sh/@google/genai@^1.9.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-brand-dark text-brand-light font-sans antialiased">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
</body>
</html>