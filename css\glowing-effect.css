/* Effet néon laser pour les cartes - Copie exacte du code qui fonctionnait */

@property --angle {
  syntax: "<angle>";
  initial-value: 0deg;
  inherits: false;
}

:root {
  --gemini-blue-light: #2190F6;
  --gemini-blue-purple: #6689EF;
  --gemini-purple: rgb(77, 70, 175);
  --gemini-salmon: rgb(235, 73, 114);
}

/* Classe utilitaire pour l'effet néon laser - EXACTEMENT comme glowing-card */
.glowing-effect {
  position: relative;
}

.glowing-effect::before,
.glowing-effect::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  width: 100%;
  height: 100%;
  border-radius: inherit;
}

.glowing-effect::before,
.glowing-effect::after {
  background-image: conic-gradient(
    from var(--angle),
    transparent 0%,
    transparent 60%,
    var(--gemini-salmon),
    var(--gemini-purple),
    var(--gemini-blue-purple),
    var(--gemini-blue-light)
  );
}

.glowing-effect::before {
  filter: blur(0.2rem) brightness(2.5);
  transform: translate(-50%, -50%) scale(1.01);
}

.glowing-effect::after {
  filter: brightness(2.5);
}

.glowing-effect::before,
.glowing-effect::after {
  opacity: 0;
  transition: opacity 0.4s ease-in-out;
}

.glowing-effect:hover::after {
  opacity: 0.3;
  animation: spin 5s linear infinite;
}

.glowing-effect:hover::before {
  opacity: 0.2;
  animation: spin 5s linear infinite;
}

@keyframes spin {
  from { --angle: 0deg; }
  to { --angle: 360deg; }
}

/* Assurer que le contenu reste au-dessus de l'effet */
.glowing-effect > * {
  position: relative;
  z-index: 1;
  background: inherit;
  border-radius: inherit;
}
