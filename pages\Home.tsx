import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

const Home: React.FC = () => {

    useEffect(() => {
        // @ts-ignore
        gsap.fromTo('.hero-text', { opacity: 0, y: 50 }, { opacity: 1, y: 0, duration: 1, stagger: 0.2, ease: 'power3.out' });
        // @ts-ignore
        gsap.fromTo('.stat-card', { opacity: 0, scale: 0.8 }, { opacity: 1, scale: 1, duration: 0.8, stagger: 0.15, scrollTrigger: { trigger: '.stats-section', start: 'top 80%' }});
    }, []);

    const services = [
      {
        icon: (
          <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="gemini-home-1" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#1A3452" />
                <stop offset="25%" stopColor="#2190F6" />
                <stop offset="50%" stopColor="#6689EF" />
                <stop offset="75%" stopColor="#8D86ED" />
                <stop offset="100%" stopColor="#AE87F3" />
              </linearGradient>
            </defs>
            <circle cx="12" cy="12" r="5" stroke="url(#gemini-home-1)" strokeWidth="2" fill="none"/>
            <path d="m15 9-6 6" stroke="url(#gemini-home-1)" strokeWidth="2" strokeLinecap="round"/>
            <path d="m9 9 .5.5" stroke="url(#gemini-home-1)" strokeWidth="2" strokeLinecap="round"/>
            <path d="m15 15-.5-.5" stroke="url(#gemini-home-1)" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        ),
        title: 'Ingénierie Prompt & Agents IA',
        description: 'Création de prompts qui engagent et d\'agents IA pour dialoguer, automatiser les tâches et analyser des données.',
      },
      {
        icon: (
          <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="gemini-home-2" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#1A3452" />
                <stop offset="25%" stopColor="#2190F6" />
                <stop offset="50%" stopColor="#6689EF" />
                <stop offset="75%" stopColor="#8D86ED" />
                <stop offset="100%" stopColor="#AE87F3" />
              </linearGradient>
            </defs>
            <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z" stroke="url(#gemini-home-2)" strokeWidth="2" fill="none"/>
            <path d="m5 3 1.5 1.5L5 6" stroke="url(#gemini-home-2)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="m19 18 1.5 1.5L19 21" stroke="url(#gemini-home-2)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        ),
        title: 'Animations Web Avancées',
        description: 'Expériences visuelles dynamiques et fluides qui captivent l\'utilisateur et renforcent l\'immersion.',
      },
      {
        icon: (
          <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="gemini-home-3" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#1A3452" />
                <stop offset="25%" stopColor="#2190F6" />
                <stop offset="50%" stopColor="#6689EF" />
                <stop offset="75%" stopColor="#8D86ED" />
                <stop offset="100%" stopColor="#AE87F3" />
              </linearGradient>
            </defs>
            <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" stroke="url(#gemini-home-3)" strokeWidth="2" fill="none"/>
            <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" stroke="url(#gemini-home-3)" strokeWidth="2" fill="none"/>
            <path d="M6 8h2" stroke="url(#gemini-home-3)" strokeWidth="2" strokeLinecap="round"/>
            <path d="M6 12h2" stroke="url(#gemini-home-3)" strokeWidth="2" strokeLinecap="round"/>
            <path d="M16 8h2" stroke="url(#gemini-home-3)" strokeWidth="2" strokeLinecap="round"/>
            <path d="M16 12h2" stroke="url(#gemini-home-3)" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        ),
        title: 'Interfaces Frontend Modernes',
        description: 'Construction d\'interfaces réactives, rapides et interactives avec une stack technique légère et à la pointe.',
      },
      {
        icon: (
          <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="gemini-home-4" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#1A3452" />
                <stop offset="25%" stopColor="#2190F6" />
                <stop offset="50%" stopColor="#6689EF" />
                <stop offset="75%" stopColor="#8D86ED" />
                <stop offset="100%" stopColor="#AE87F3" />
              </linearGradient>
            </defs>
            <path d="M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z" stroke="url(#gemini-home-4)" strokeWidth="2" fill="none"/>
            <path d="M22 10a3 3 0 0 0-3-3h-2.207a5.502 5.502 0 0 0-10.702.5" stroke="url(#gemini-home-4)" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        ),
        title: 'Infrastructure Backend Serverless',
        description: 'Mise en place de backends robustes et scalables sans gestion de serveur grâce à l\'écosystème serverless.',
      }
    ]

    const stats = [
        { value: '15+', label: 'Années d\'expérience de Codage' },
        { value: '1.5M+', label: 'Lignes de Code Écrites' },
        { value: '8 000+', label: 'Vidéos, links & Ressources UX/Dev' },
        { value: '680+', label: 'Outils, Templates & Créatifs Testés' }
    ]

    return (
        <div className="container mx-auto px-6 py-20 text-center">
            <div className="py-24">
                <h1 className="hero-text text-5xl md:text-7xl font-bold bg-clip-text text-transparent bg-gradient-to-br from-white to-brand-muted leading-tight">
                    Développeur de Solutions IA<br />& Full-Stack Créatif
                </h1>
                <p className="hero-text text-xl md:text-2xl text-brand-muted mt-6 max-w-3xl mx-auto">
                    De l'idée à la réalité. Je conçois des solutions web innovantes où l'intelligence artificielle rencontre un design créatif.
                </p>
                <div className="hero-text mt-10 flex justify-center gap-4">
                    <Link to="/portfolio" className="bg-gradient-to-r from-brand-blue to-brand-purple text-white font-bold py-3 px-8 rounded-lg hover:scale-105 transition-transform">
                        Voir Mes Réalisations
                    </Link>
                    <Link to="/contact" className="bg-brand-surface text-white font-bold py-3 px-8 rounded-lg border border-brand-surface-light hover:border-brand-purple transition-all">
                        Discutons de votre projet
                    </Link>
                </div>
            </div>

            <section className="stats-section py-20">
                 <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    {stats.map((stat, i) => (
                        <div key={i} className="stat-card bg-brand-surface/50 backdrop-blur-sm border border-brand-surface rounded-lg p-8 text-center">
                            <p className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-brand-blue to-brand-purple">{stat.value}</p>
                            <p className="text-brand-muted mt-2">{stat.label}</p>
                        </div>
                    ))}
                </div>
            </section>

            <section className="py-12 sm:py-20 px-4 sm:px-6 lg:px-8">
                <h2 className="text-2xl xs:text-3xl sm:text-4xl font-bold mb-4 text-center">L'alliance parfaite entre Intelligence Artificielle et Créativité.</h2>
                <p className="text-base xs:text-lg sm:text-xl text-brand-muted max-w-4xl mx-auto mb-8 sm:mb-12 text-center px-4">
                    Dans un monde où la technologie évolue à une vitesse fulgurante, je vous accompagne pour transformer vos idées en solutions digitales, innovantes. Mon expertise couvre le développement full-stack, le design d'expérience utilisateur et l'intégration d'IA pour créer des expériences exceptionnelles.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8 text-left px-4 sm:px-0">
                    {services.map((service, i) => (
                         <div key={i} className="bg-brand-surface/50 backdrop-blur-sm border border-brand-surface p-4 xs:p-6 sm:p-8 rounded-lg hover:border-brand-blue transition-all cursor-pointer">
                            <div className="mb-4">{service.icon}</div>
                            <h3 className="text-lg xs:text-xl font-bold text-white mb-2">{service.title}</h3>
                            <p className="text-sm xs:text-base text-brand-muted">{service.description}</p>
                        </div>
                    ))}
                </div>
                <div className="mt-8 sm:mt-12 text-center px-4">
                     <Link to="/competences" className="inline-block bg-transparent border-2 border-brand-blue text-brand-blue font-bold py-3 px-4 xs:px-6 sm:px-8 rounded-lg hover:bg-brand-blue hover:text-white transition-all text-sm xs:text-base whitespace-nowrap">
                        <span className="hidden sm:inline">Découvrir toutes mes compétences</span>
                        <span className="sm:hidden">Mes compétences</span>
                    </Link>
                </div>
            </section>
        </div>
    );
};

export default Home;