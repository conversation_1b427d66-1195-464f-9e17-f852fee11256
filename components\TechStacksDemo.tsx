import React, { useState, useEffect, useRef } from 'react';
import '../css/glowing-effect.css';

// --- Helper pour parser le texte ---
const FormattedText = ({ text }) => {
    if (!text) return null;
    const parts = text.split(/(`[^`]+`|\*\*.*?\*\*)/g).filter(Boolean);
    return (
        <span>
            {parts.map((part, i) => {
                if (part.startsWith('`')) {
                    return <code key={i} className="bg-black/20 text-blue-300 rounded-md px-2 py-1 text-sm font-mono">{part.slice(1, -1)}</code>;
                }
                if (part.startsWith('**')) {
                    return <strong key={i}>{part.slice(2, -2)}</strong>;
                }
                return part;
            })}
        </span>
    );
};

// --- Interfaces TypeScript ---
interface IStack { type: string; description: string; technologies: string; reason: string; }
interface IFullStack { useCase: string; frontend: string; backend: string; deployment: string; synergies: string; }

// --- Données ---
const stacksData: IStack[] = [
    { type: 'Stack "Légère & Interactive"', description: 'Idéal pour sites vitrines modernes, portfolios, landing pages animées', technologies: '`Vite.js` + `Tailwind CSS` + `Alpine.js` + `GSAP` + `Lenis`', reason: "C'est le combo parfait pour la performance et la créativité. **Vite** offre un serveur de dev ultra-rapide. **Tailwind** permet de styler sans quitter le HTML. **Alpine.js** ajoute l'interactivité. **GSAP** gère les animations complexes. On y ajoute **Lenis** pour un défilement fluide et professionnel, et le combo est parfait." },
    { type: 'Application Frontend (React)', description: 'Pour des applications web complexes, dashboards, plateformes SaaS', technologies: '`React` + `Vite.js` + `Tailwind CSS` + `Zustand` ou `Redux Toolkit`', reason: "C'est la stack la plus populaire pour les grosses applications. **React** construit l'interface utilisateur en composants. **Vite** compile le tout à la vitesse de l'éclair. **Tailwind** assure un design cohérent. **Zustand** (léger) ou **Redux** (plus complet) gère l'état global de l'application." },
    { type: 'Application Full-Stack "Serverless"', description: "Quand on a besoin d'un backend sans gérer de serveur", technologies: '`Stack Frontend au choix` (React/Vue/etc.) + `Firebase`', reason: "Cette approche est très moderne. Vous construisez votre frontend avec votre stack préférée, et **Firebase** s'occupe de tout le reste : base de données (Firestore), authentification des utilisateurs, stockage de fichiers et même hébergement." },
    { type: 'Scène 3D / Expérience WebGL', description: 'Pour des sites immersifs, configurateurs de produits, jeux', technologies: '`Three.js` + `React (via react-three-fiber)` + `GSAP` + `Lenis`', reason: "**Three.js** est la bibliothèque de référence pour la 3D sur le web. L'utiliser avec **react-three-fiber** permet de construire la scène de manière déclarative. **GSAP** pilote les animations. L'ajout de **Lenis** peut rendre la navigation et le défilement dans ces scènes immersives beaucoup plus agréables." },
];
const fullStacksData: IFullStack[] = [
    { useCase: 'Expérience 3D / Site Immersif', frontend: '`React` + `TypeScript` + `Three.js (via react-three-fiber)` + `GSAP` + `Tailwind CSS`', backend: '**Option A (Léger) :** `Firebase`\n**Option B (API custom) :** `Node.js` + `Express` + `TypeScript`', deployment: '**Frontend :** Vercel, Netlify.\n**Backend :** Firebase Hosting, Render...', synergies: "C'est la stack que tu as décrite. **TypeScript** sécurise les interactions complexes. **react-three-fiber** rend l'utilisation de Three.js dans React incroyablement intuitive." },
    { useCase: 'Application d\'Entreprise / SaaS', frontend: '`Next.js` + `TypeScript` + `Tailwind CSS` + `shadcn/ui` + `Zustand`', backend: '**Option A (Intégré) :** `API Routes de Next.js`\n**Option B (Dédié) :** `Node.js` + `Fastify` + `Prisma`', deployment: '**Option A :** `Vercel`.\n**Option B :** `Docker` sur AWS/GCP, Render...', synergies: 'La stack "tout-en-un" pour des applications sérieuses. **Next.js** gère le front et le back. **TypeScript** est utilisé sur toute la ligne, assurant une cohérence totale.' },
    { useCase: 'SPA classique avec API dédiée', frontend: '`React` + `TypeScript` + `Vite.js` + `Tailwind CSS` + `TanStack Query`', backend: '`Node.js` + `Express` + `TypeScript` + `Drizzle ORM`', deployment: '**Frontend :** Netlify, Firebase Hosting.\n**Backend :** Service dédié (Render, Fly.io...).', synergies: "Cette architecture sépare clairement le frontend du backend. **Vite** offre une expérience de développement rapide. **TanStack Query** est la référence pour gérer l'état du serveur." },
];

// --- NOUVEAU: Composant pour l'animation de fond ---
const BackgroundAnimation = () => {
    const canvasRef = useRef(null);

    useEffect(() => {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        let particles = [];

        const resizeCanvas = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        };

        const createParticle = () => ({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            vx: (Math.random() - 0.5) * 0.5,
            vy: (Math.random() - 0.5) * 0.5,
            size: Math.random() * 2 + 1,
            opacity: Math.random() * 0.5 + 0.2
        });

        const initParticles = () => {
            particles = [];
            for (let i = 0; i < 50; i++) {
                particles.push(createParticle());
            }
        };

        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            particles.forEach(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                
                if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
                
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(33, 144, 246, ${particle.opacity})`;
                ctx.fill();
            });
            
            requestAnimationFrame(animate);
        };

        resizeCanvas();
        initParticles();
        animate();

        window.addEventListener('resize', () => {
            resizeCanvas();
            initParticles();
        });

        return () => {
            window.removeEventListener('resize', resizeCanvas);
        };
    }, []);

    return (
        <canvas 
            ref={canvasRef}
            className="absolute inset-0 pointer-events-none opacity-30"
            style={{ zIndex: -1 }}
        />
    );
};

// --- Composant Titre de Section ---
const SectionTitle = ({ children }) => (
    <h2 className="text-4xl md:text-5xl font-extrabold mb-12 text-center text-transparent bg-clip-text bg-gradient-to-r from-[#2190F6] via-[#6689EF] to-[#AE87F3] drop-shadow-lg">
        {children}
    </h2>
);

// --- Composant Carte Stack ---
const StackCard = ({ stack, scriptsLoaded }) => {
    const [isHovered, setIsHovered] = useState(false);

    return (
        <div className="glowing-effect bg-brand-surface rounded-2xl p-8 border border-brand-surface">
            <div>
                <h3 className="text-2xl font-bold mb-4 text-white group-hover:text-[#2190F6] transition-colors duration-300">
                    {stack.type}
                </h3>
                <p className="text-gray-300 mb-4 leading-relaxed">
                    {stack.description}
                </p>
                <div className="mb-6">
                    <h4 className="text-lg font-semibold text-[#6689EF] mb-2">Technologies :</h4>
                    <div className="text-gray-200">
                        <FormattedText text={stack.technologies} />
                    </div>
                </div>
                <div>
                    <h4 className="text-lg font-semibold text-[#AE87F3] mb-2">Pourquoi cette stack ?</h4>
                    <div className="text-gray-300 leading-relaxed">
                        <FormattedText text={stack.reason} />
                    </div>
                </div>
            </div>
        </div>
    );
};

// --- Composant Carte Full Stack ---
const FullStackCard = ({ stack, scriptsLoaded }) => {
    const [isExpanded, setIsExpanded] = useState(false);

    return (
        <div className="glowing-effect bg-brand-surface rounded-2xl p-8 border border-brand-surface">
            <div>
                <h3 className="text-2xl font-bold mb-4 text-white group-hover:text-[#2190F6] transition-colors duration-300">
                    {stack.useCase}
                </h3>

                <div className="space-y-4">
                    <div>
                        <h4 className="text-lg font-semibold text-[#2190F6] mb-2">Frontend :</h4>
                        <div className="text-gray-200">
                            <FormattedText text={stack.frontend} />
                        </div>
                    </div>

                    <div>
                        <h4 className="text-lg font-semibold text-[#6689EF] mb-2">Backend :</h4>
                        <div className="text-gray-200 whitespace-pre-line">
                            <FormattedText text={stack.backend} />
                        </div>
                    </div>

                    <div>
                        <h4 className="text-lg font-semibold text-[#AE87F3] mb-2">Déploiement :</h4>
                        <div className="text-gray-200 whitespace-pre-line">
                            <FormattedText text={stack.deployment} />
                        </div>
                    </div>

                    <div>
                        <h4 className="text-lg font-semibold text-[#D65E65] mb-2">Synergies :</h4>
                        <div className="text-gray-300 leading-relaxed">
                            <FormattedText text={stack.synergies} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

// --- Composant Mémo Dev ---
const DevMemo = () => (
    <div className="glowing-effect my-16 p-8 bg-brand-surface rounded-2xl border border-brand-surface">
        <div className="flex items-start space-x-4">
            <div className="flex-shrink-0 w-8 h-8 bg-[#2190F6] rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">💡</span>
            </div>
            <div>
                <h3 className="text-xl font-bold text-[#2190F6] mb-3">Note du développeur</h3>
                <p className="text-gray-300 leading-relaxed">
                    Ces stacks sont mes recommandations basées sur l'expérience. Chaque projet est unique, 
                    et le choix final dépend toujours de vos contraintes spécifiques : budget, délais, 
                    équipe, maintenance future, etc. N'hésitez pas à me contacter pour discuter de votre projet !
                </p>
            </div>
        </div>
    </div>
);

// --- Composant Principal ---
function App() {
    const [scriptsLoaded, setScriptsLoaded] = useState(false);

    useEffect(() => {
        setScriptsLoaded(true);
    }, []);

    return (
        <div className="text-white relative">
            <BackgroundAnimation />

            <div className="relative z-10 container mx-auto px-6 py-16">
                <main>
                    <section className="mb-16">
                        <SectionTitle>Combinaisons de Technologies Web</SectionTitle>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            {stacksData.map((stack) => <StackCard key={stack.type} stack={stack} scriptsLoaded={scriptsLoaded} />)}
                        </div>
                    </section>

                    <DevMemo />

                    <section>
                        <SectionTitle>Focus : Stacks Complètes avec React & TypeScript</SectionTitle>
                        {/* Passage en grille responsive pour les cartes */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-stretch">
                            {fullStacksData.map((stack) => <FullStackCard key={stack.useCase} stack={stack} scriptsLoaded={scriptsLoaded} />)}
                        </div>
                    </section>
                </main>
            </div>
        </div>
    );
}

// --- Composant principal exporté pour usage externe ---
export function TechStacksDemo() {
    return <App />;
}
